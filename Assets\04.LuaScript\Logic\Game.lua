---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2021 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------
---
---=====================================================================
---此區段請勿調整順序---
require("Global/GDefines")
if ProjectMgr.IsEditor then
    require("Common/LuaDebug")
end
---此區段請勿調整順序---

---擴充引用
require("Common/table")
require("Common/string")
require("Common/Coroutine")

require("Global/GFunction")
require("Global/GConsts")
require("Global/GEnums")

require("InheritedBase/DebugMsgBase")
require("Logic/Mgr/NetworkMgr")
require("Logic/Mgr/DownloadFileMgr")
require("Logic/Mgr/JsonMgr")
require("Logic/Mgr/CameraMgr")
require("Logic/Mgr/UI/UIMgr")
require("Logic/Mgr/Data/DataMgr")
require("Logic/Mgr/Room/RoomMgr")
require("Logic/Mgr/AudioMgr/AudioMgr")
require("Logic/Role/RoleMgr")

---遊戲主邏輯
---包含所有 logic Update
---author KK
---version 1.0
---since [ProjectBase] 0.1
---date 2021.10.12
Game = {}

---Game's local Table
local this = {}

---2023.12.19 Add by KK
---請跟 C# 內 BuildData_Default.m_Bundle_Version_Code 同步, ***已經拆耦合了***
---不要再共用同個變數
---@type number 當前Bundle版本號
Game.m_Bundle_Version_Code = 1
---Function
function Game.Init(iCallback_Finish)
    ---會因為語言切換的串表 須根據當前語言選擇串表要參考的檔案
    -- DataMgr.ReassignedFileAccordingToLanguage()
    ---串檔初始化

    ---網路初始化
    NetworkMgr.Init()
    ---先讀取本機檔案 ( 副檔名得是 .txt )
    local _ServerList = JsonMgr.LoadFromResources("ServerInfo/ServerList")
    -- 測試連線 OK
    local _IP = _ServerList[1].IP
    local _Port = tonumber(_ServerList[1].PORT)
    -- ClientSocket.Connect(_IP, _Port)

    ---下載管理器初始化 這個 lua 需要做調整
    -- DownloadFileMgr.Init()

    ---優先取得ClientSaveMgr資料 因為需要裡面紀錄的語言相關資訊
    -- ClientSaveMgr.Init()
    ---用是否已經簽過合約作為第一次開起遊戲的判斷
    -- local _ShowPanel =  not ClientSaveMgr.GetDataValue(EClientSaveDataType.Device,"m_IsAgreementPermitted")
    -- CameraMgr.Init()
    -- if _ShowPanel == true then
    --	Game.ShowSelectLanguagePanel(iCallback_Finish)
    -- else
    --	Game.InitPostLanguageSelect(iCallback_Finish)
    -- end

    -- region 讀取單 & Group 資源範例
    -- ResourceMgr.Load( "MapIconScrObj", Game.LoadTest )
    -- ResourceMgr.LoadGroup( "CinemachineNoise", Game.LoadGroupTest )
    -- endregion

    ---串檔初始化
    DataMgr.Init(function(iErrorMsg)
        local _IsLoadDataSuccess = iErrorMsg == nil
        if not _IsLoadDataSuccess then
            D.LogError("[DataMgr] 初始化失敗!!")
        else
            UIMgr.Init()
            Button.InitButtonDelegateMgr()
            -- region Data 測試Code
            local _Current = TextData:GetDataByIndex(1)
            local _CurrentText = TextData:Get(1)
            D.Log("_Current:ToString():" .. _Current:ToString())
            D.Log("_CurrentText:" .. _CurrentText)
            -- endregion
            UIMgr.Open(EUIPrefabName.HeroSelect)
            RoomMgr.Init()
			RoleMgr.Init()
			AudioMgr.Init()
            if iCallback_Finish then
                iCallback_Finish()
            end
        end
    end)
end

function Game.Update()
    DataMgr.Update()
    UIMgr.Update()
	RoleMgr.Update()
    --D.Log( "Game.Update..." )
end

---主要處理邏輯 Update
function Game.FixedUpdate()
    -- D.Log( "Game.FixedUpdate..." )
    --[[
	UIMgr.FixedUpdate()
	HEMTimeMgr.FixedUpdate()
	--]]
	RoleMgr.FixedUpdate()
end

function Game.LateUpdate()
    -- D.Log( "Game.LateUpdate..." )
    --[[
	NPCMgr.LateUpdate()
	HEMTimeMgr.LateUpdate()
	CameraMgr.LateUpdate()
	--]]
	RoleMgr.LateUpdate()
end

function Game.OnApplicationFocus(iIsFocus)
    D.Log("Game.OnApplicationFocus: " .. (iIsFocus and "true" or "false"))
end

function Game.OnApplicationPause(iIsPause)
    D.Log("Game.OnApplicationPause: " .. (iIsPause and "true" or "false"))
end

function Game.OnApplicationQuit()
    D.Log("Game.OnApplicationQuit")
    -- SearchMgr.Destroy()
    -- CullingGroupMgr.Inst:Dispose()
end

-- 讀取範例
function this.LoadTest(iObjs)
    local _Table = iObjs.m_Dict_MapIcon
    for _Key, _Value in pairs(_Table) do
        print("Key: " .. _Key .. "     Value: " .. _Value.m_Index)
    end
end

function this.LoadGroupTest(iObjs)
    for i = 0, iObjs.Length - 1 do
        print("SignalDuration: " .. iObjs[i].SignalDuration)
    end
end

--[[
---顯示語言選擇面板
function Game.ShowSelectLanguagePanel(iCallback_Finish)

	---呼叫Unity的CommonQuery
	local _TempUI = {}
	_TempUI.gameObject = GameObject.FindGameObjectWithTag("UIMgr")
	_TempUI.m_Transform_Peak = _TempUI.gameObject.Find("Peak").transform
	local _CurrentGameObject = _TempUI.m_Transform_Peak:GetComponent("Transform"):Find("ExeCommonQuery_View").gameObject
	local _UICommonQuery = _CurrentGameObject:GetComponent("UICommonQuery")

	local function OnComfirmClick( iBtnType)
		ClientSaveMgr.ChangeDataValue(EClientSaveDataType.Device,"m_LastLanguage", (_UICommonQuery:GetDropdownValue() ))
		Game.InitPostLanguageSelect(iCallback_Finish)
	end

	_UICommonQuery:SetLanguageSelectFromLua(OnComfirmClick,true)

	_CurrentGameObject:SetActive(true)
end


---選語言後的 後半段初始化流程
function Game.InitPostLanguageSelect(iCallback_Finish)
	---會因為語言切換的串表 須根據當前語言選擇串表要參考的檔案
	DataMgr.ReassignedFileAccordingToLanguage()
	---串檔初始化
	DataMgr.Init(function(iErrorMsg)
		local _IsLoadDataSuccess = iErrorMsg == nil
		if not _IsLoadDataSuccess then
			D.LogError("[DataMgr] 初始化失敗!!")
		end
	end)

	---系統初始化
	SettingMgr.Init()
	SelectMgr.Init()
	RoleMgr.Init()
	SceneMgr.Init()
	RedPointMgr.Init()
	NPCMgr.Init()
	GearMgr.Init()
	EffectMgr.Init()
	DropMgr.Init()
	HEMTimeMgr.Initialize()
	BattleMgr.Init()
	AutoBattleMgr.Init()
	CDMgr.Init()
	MapMgr.Init()
	TeachMgr.Init()
	HUDController.Init()
	BuffMgr.Init()
	AppearanceMgr.Init()
	PopItemChange_Mgr.Init()
	MessageMgr.Init()
	CommonQueryMgr.Init()
	TMPMgr.Init()
	RecruitMgr.Init()
	---獨體初始化
	CinemachineMgr.Inst:Do()

	IconMgr.Init()

	AudioMgr.Init(
		function()
			---UI最後初始化
			UIMgr.Init()
			
			coroutine.start(function ()
				while not TMPMgr.m_IsTMPStyleSheetInited do
					coroutine.wait(0.1)
				end
	
				UIMgr.OpenLoading(ELoadingOpenType.Defult, Login_Controller,"","",true)
				UIMgr.Open(UIBlackBase_Controller)
	
	
				--Loading_Controller.SetDebugInfo( GameTools.ResTextGetter.GetResText( ELanguageType.TraditionalChinese, "GameInit" ))
	
				if iCallback_Finish then
					iCallback_Finish:Invoke()
				end
			end)
		end
	)

end

function Game.OpenLoginUI()
	UIMgr.Open(Login_Controller)
	if not ProjectMgr.IsRelease() then
		UIMgr.Open(Debug_Controller)
	end
	--開完後刷新 Login 的 Order
	coroutine.start(function()
		coroutine.wait(0.5)
		UIMgr.UpdateUIOrderByUIController(Login_Controller)
	end)
end

function Game.Update()
	---D.Log( "toLua game updateing..." );
	UIMgr.Update()
	RoleMgr.Update()
	NPCMgr.Update()
	SceneMgr.Update()
	MapMgr.Update()
	DataMgr.Update()
	SelectMgr.Update()
	HEMTimeMgr.Update()
	HotKeyMgr.Update()
	CDMgr.Update()
	EffectMgr.Update()
	AudioMgr.Update()
	BubbleMgr.Update()
	PopItemChange_Mgr.Update()
	MessageMgr.Update()
	DropMgr.Update()
	---2022.07.28 Add by KK Test Code
	SearchMgr.Update()

	AppearanceMgr.Update()
end




---登入成功後要做甚麼
function Game.AfterSuccessfulLogin(iPacket)
	Login_Model.m_IsLogin = true
	UIMgr.Open(UIOrderTop_Controller)
	local _LoginPlayerData = PlayerData.InitLoginPlayerData(iPacket)
	local _CreateRoleData = RoleCreateData:NewPlayer(_LoginPlayerData)
	_CreateRoleData.m_CallBack = function(iRC)
		CameraMgr.InitCinemachine()
		CameraMgr.SetMainTarget(iRC.transform)
	end

	HotKeyMgr.Init()
	RoleMgr.CreatePlayer(PlayerData.GetRoleID(), _CreateRoleData)
	PlayerData.Get(EPalyerData.Flags).SetNotify("StaticFlag_Init", function()
		-- Receive_003_003_Notify()
		-- 每次登入都檢查,是否有排行榜紅點 日清永標 稱號

		---Test Data
		RedPointMgr.ChangeRedpointCount(RedPointMgr.m_NodeNames.Menu_Bag_Weapon, 1)
		RedPointMgr.ChangeRedpointCount(RedPointMgr.m_NodeNames.Menu_Bag_Clothes, 1)
		RedPointMgr.ChangeRedpointCount(RedPointMgr.m_NodeNames.Menu_Bag_Necklace, 1)
		RedPointMgr.ChangeRedpointCount(RedPointMgr.m_NodeNames.Menu_Bag_Dia, 1)
		RedPointMgr.ChangeRedpointCount(RedPointMgr.m_NodeNames.Menu_Bag_Item, 1)
		RedPointMgr.ChangeRedpointCount(RedPointMgr.m_NodeNames.Main_Guild, 1)
	end)

	AppearanceMgr.CreateDefault()
	AlertRangeMgr:Init()
	ClientSaveMgr.LoadClientFileAfterLogin()
	--還需要初始化最愛道具
	ItemHint_Model.SetLoveItemInit()
	TimeMachineMgr.Init()

	ChatMgr.Init()

	AutoBattleMgr.Login()

	---生成稱號權限列表
	PlayerData_Title.BuildTitleTable()
	---武裝升級初始化
	EnergyMineMgr.Init()
end

---登出要做甚麼 都放在這唷喔喔
function Game.LoggingOutToDo()
	if ProjectMgr.IsDebug then
		GoldFinger_Controller.LoggingOutToDo()
	end
	SceneMgr.LogOutReset()
	BuffMgr.ClearAllData()
	AppearanceMgr.ReleaseAll(true)
	RoleMgr.LoggingOutToDo()
	SearchMgr.ClearSearchTarget()
	CullingGroupMgr.Inst:Dispose()
	BattleMgr.LogOutReset()
	BagMgr.LogOutClean()
	PlayerData.LogOutClean()
	PlayerData_Flags.LogOutClean()
	ChatMgr.LoggingOutToDo()
	HotKeyMgr.LogOutClear()
	EnergyMineMgr.LogOutClean()
	AutoBattleMgr.LogOutClean()
	--=====請加在上方====
	ClientSaveMgr.LoggingOutToDo()
	UIMgr.LoggingOutToDo()

	---根據是因為語言切換的登出 做相關流程的處理
	if Setting_Controller.m_IsLogingOutChangeLanguage == true then
		Game.LoggingOutLastStepFromLanguage()
	else
		Game.LoggingOutLastStep()
	end

end

---換日後 第一次接收到時間更新時要做甚麼
function Game.ToDoFirstReceiptTimeAfterDateChange()

end

---因為切換語言所以必須登出時 需要特別處理某些data/UI
function Game.ReInitialDateFromLanguageChange()

	---重新設定 TMP Style 以及 material
	local _ELanguage = ClientSaveMgr.GetDataValue(EClientSaveDataType.Device,"m_LastLanguage")
	TMPMgr.ChangeLanguage(_ELanguage)

	---會因為語言切換的串表 須根據當前語言選擇串表要參考的檔案
	DataMgr.ReassignedFileAccordingToLanguage()

	---一般串檔table資料設成未讀取狀態
	for key, value in pairs(DataModel.m_ChangeLanguageRnewTable) do
		value.m_IsDataInitialized = false
		value.m_DataCount = 0
		value.m_NewDataCount = 0
		value.m_StreamReadByteTimes = 0
	end

	---事件串檔table資料設成未讀取狀態
	for key, value in pairs(EventDataModel.m_ChangeLanguageRenewTable) do
		value.m_IsDataInitialized = false
		value.m_DataCount = 0
		value.m_NewDataCount = 0
		value.m_StreamReadByteTimes = 0
	end

	---一般串檔table資料開始初始化
	for key, value in pairs(DataModel.m_ChangeLanguageRnewTable) do
		value.Init()
	end
	---事件串檔table資料開始初始化
	for key, value in pairs(EventDataModel.m_ChangeLanguageRenewTable) do
		value.Init()
	end

end

---切換語言的登出 需要處理的項目
function Game.LoggingOutLastStepFromLanguage()

	---換語言要重新登入bool 設成true
	Login_Model.m_ReLogin_Language = true
	--UIMgr.LoggingOutToDo()
	UIMgr.DestroyAllUI()
	Game.ReInitialDateFromLanguageChange()
	UIMgr.OpenLoading(ELoadingOpenType.Defult, Login_Controller,"","",true)
	---設定換語言切換專屬的檔案讀取檢查
	DataMgr.SetProcessFunc()
	coroutine.start(
		function()
				---還沒讀完就先卡住
				while DataMgr.CheckIsLanguageRelativeDataInitFinish() == false do
					coroutine.wait(1)
				end
				Game.LoggingOutLastStep()
				coroutine.stop()
			end)
end

---登出最後一個步驟 開啟登入介面 關閉相關bool
function Game.LoggingOutLastStep()
	Game.OpenLoginUI()
	Login_Model.m_IsLogin = false
	Setting_Controller.m_IsLogingOut = false
	Setting_Controller.m_IsLogingOutChangeLanguage = false
end
--]]
