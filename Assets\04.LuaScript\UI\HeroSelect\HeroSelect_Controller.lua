---=====================================================================
---              LAN YONG PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with LAN YONG and may not 
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2025 by LAN YONG.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------    
---
---=====================================================================
---私有變數
local private = {}

---英雄選擇 UI
---@class HeroSelect_Controller
---author <PERSON><PERSON>ee
---telephone 0939
---version 0.01
---since UnityProjectBase
---date 2025.5.13
HeroSelect_Controller = {}
---自己的 UIController
private.m_UIController = HeroSelect_Controller
---private New Base
UIControllerBase.New(private.m_UIController, EUIPrefabName.HeroSelect, EFormType.DefaultFullForm, "bg_007")

---@type ECharacterType
---當前選擇的英雄
private.m_CurSelectHero = ECharacterType.None
---region UI 物件宣告
---@type table<number, RawImage>
private.m_RImage_Hero = {}
---@type TextMeshProUGUI
private.m_TMPText_HeroName = nil
---@type ButtonEx
private.m_Button_Battle = nil
---endregion

---初始化
function HeroSelect_Controller:Init(iViewRef)
    -- 初始化英雄圖片

    private.m_RImage_Hero[ECharacterType.Gladiator] = iViewRef.m_Dic_RawImage:Get("&RImage_Hero_1")
    private.m_RImage_Hero[ECharacterType.PlagueDoctor] = iViewRef.m_Dic_RawImage:Get("&RImage_Hero_2")
    private.m_RImage_Hero[ECharacterType.ElfArcher] = iViewRef.m_Dic_RawImage:Get("&RImage_Hero_3")

    for i, _HeroImage in pairs(private.m_RImage_Hero) do
        Button.AddListener(_HeroImage, EventTriggerType.PointerClick, function()
            private.OnClick_Hero(i)
        end)
    end

    private.m_TMPText_HeroName = iViewRef.m_Dic_TMPText:Get("&TMPText_HeroName")
    private.m_Button_Battle = iViewRef.m_Dic_ButtonEx:Get("&Button_Battle")
    Button.AddListener(private.m_Button_Battle, EventTriggerType.PointerClick, private.OnClick_Battle)
end

function HeroSelect_Controller:Open(iParams)
    private.m_CurSelectHero = ECharacterType.Gladiator
    -- 初始化英雄選擇畫面
    private.RefreshHeroInfo()

    AudioMgr.PlayBGM(AudioSetting.m_PlayingBGMKind.HeroSelect)

    return true
end

function private.RefreshHeroInfo()

    private.m_TMPText_HeroName.text = TextData:Get(1000 + private.m_CurSelectHero)
    -- 更新英雄圖片顏色
    for i, _HeroImage in pairs(private.m_RImage_Hero) do
        if i == private.m_CurSelectHero then
            _HeroImage.color = Color(1, 1, 1, 1)
        else
            _HeroImage.color = Color(0.3, 0.3, 0.3, 1)
        end
    end
end

function private.OnClick_Hero(iIndex)
    -- 防止重複點選
    if private.m_CurSelectHero == iIndex then
        return
    end

    -- 處理英雄選擇
    D.Log("英雄選擇: " .. iIndex)
    private.m_CurSelectHero = iIndex
    private.RefreshHeroInfo()
end

function private.OnClick_Battle()
    -- 處理戰鬥按鈕點擊事件
    D.Log("開始戰鬥: " .. private.m_CurSelectHero)
    UIMgr.Open(EUIPrefabName.RoomSelect, private.m_CurSelectHero)
end

return HeroSelect_Controller