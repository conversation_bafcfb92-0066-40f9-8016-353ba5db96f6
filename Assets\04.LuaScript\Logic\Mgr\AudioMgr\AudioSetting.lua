---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2022 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------
---
---=====================================================================

---音效設定 參數控制
---@class AudioSetting
---author Hui
---version 1.0
---since [ProjectBase] 0.1
---date 2025.5.20

AudioSetting = {}

--- 最大可同時播放音效數量
AudioSetting.m_MaxAudioPlayingCount = { Low = 15, Medium = 25, High = 40 }
--- 最大物件池可容納總音效數量
AudioSetting.m_MaxAudioPoolCount = { Low = 20, Medium = 35, High = 50 }

--- 淡出速度
AudioSetting.m_FadeOutSpeed = 0.3
--- 淡入速度
AudioSetting.m_FadeInSpeed = 0.5
--- 淡入音效從淡出音效聲音大小多少後開始淡入
AudioSetting.m_FadeInStartVolume = 0.5
--- 幾秒掃一次 AudioPool
AudioSetting.m_RefreshAudioPoolSecond = 1

---@class m_PlayingBGMKind 特殊背景音效種類 (音效編號 )
AudioSetting.m_PlayingBGMKind =
{
    --- 下載時的背景音效
    DownLoadBGM = 105,
    --- 登入時的背景音效
    LoginBGM = 1,
    HeroSelect = 1,
}

--- Audio Source 初始值
AudioSetting.m_DefalutAudioSourceSetting =
{
    MinDistance = 1,
    MaxDistance = 500,
    Spread = 0,
    SpatialBlend = 0,
    RolloffMode = AudioRolloffMode.Logarithmic,
    -- EnvironmentSound.AnimationCurve)
}

--- 混音器容許最高增幅分貝
AudioSetting.AudioMixerDB_Highest = 20
--- 混音器容許最低降幅分貝 
AudioSetting.AudioMixerDB_Lowest = -80 

--- Unity 混音器容許調整範圍 -80db ~ +20db
AudioSetting.m_AudioVolumeRange = {
    DefaultMaster = {Max = 5, Min = -40},
    DefaultUI = {Max = -6, Min = -40},
    DefaultBGM = {Max = -6.5, Min = -38},
    DefaultSFX = {Max = 2, Min = -38},
}

--- 預設音效開關
AudioSetting.m_DefaultAudioSwitch = {
    DefaultMaster = true,
    DefaultUI = true,
    DefaultBGM = true,
    DefaultSFX = true,
}

--- 預設音效音量 ( 0 ~ 1 )
AudioSetting.m_DefaultAudioVolume = {
    DefaultMaster = 0.8,    --0.8,
    DefaultUI = 1,        --0.83,
    DefaultBGM = 0.25,       --0.83,
    DefaultSFX = 1,       --0.8,
}

AudioSetting.m_AudioSwitch = {}
AudioSetting.m_AudioSwitch[table.GetKey(AudioMgr.EMixerGroup, AudioMgr.EMixerGroup.Master)] = AudioSetting.m_DefaultAudioSwitch.DefaultMaster
AudioSetting.m_AudioSwitch[table.GetKey(AudioMgr.EMixerGroup, AudioMgr.EMixerGroup.UI)] = AudioSetting.m_DefaultAudioSwitch.DefaultUI
AudioSetting.m_AudioSwitch[table.GetKey(AudioMgr.EMixerGroup, AudioMgr.EMixerGroup.BGM)] = AudioSetting.m_DefaultAudioSwitch.DefaultBGM
AudioSetting.m_AudioSwitch[table.GetKey(AudioMgr.EMixerGroup, AudioMgr.EMixerGroup.SFX)] = AudioSetting.m_DefaultAudioSwitch.DefaultSFX

--- 音效聲音大小  ( 0 ~ 1 )
AudioSetting.m_AudioVolume = {}
AudioSetting.m_AudioVolume[table.GetKey(AudioMgr.EMixerGroup, AudioMgr.EMixerGroup.Master)] = AudioSetting.m_DefaultAudioVolume.DefaultMaster
AudioSetting.m_AudioVolume[table.GetKey(AudioMgr.EMixerGroup, AudioMgr.EMixerGroup.UI)] = AudioSetting.m_DefaultAudioVolume.DefaultUI
AudioSetting.m_AudioVolume[table.GetKey(AudioMgr.EMixerGroup, AudioMgr.EMixerGroup.BGM)] = AudioSetting.m_DefaultAudioVolume.DefaultBGM
AudioSetting.m_AudioVolume[table.GetKey(AudioMgr.EMixerGroup, AudioMgr.EMixerGroup.SFX)] = AudioSetting.m_DefaultAudioVolume.DefaultSFX

AudioSetting.m_AudioMaxVolume = {}
AudioSetting.m_AudioMaxVolume[table.GetKey(AudioMgr.EMixerGroup, AudioMgr.EMixerGroup.Master)] = AudioSetting.m_AudioVolumeRange.DefaultMaster
AudioSetting.m_AudioMaxVolume[table.GetKey(AudioMgr.EMixerGroup, AudioMgr.EMixerGroup.UI)] = AudioSetting.m_AudioVolumeRange.DefaultUI
AudioSetting.m_AudioMaxVolume[table.GetKey(AudioMgr.EMixerGroup, AudioMgr.EMixerGroup.BGM)] = AudioSetting.m_AudioVolumeRange.DefaultBGM
AudioSetting.m_AudioMaxVolume[table.GetKey(AudioMgr.EMixerGroup, AudioMgr.EMixerGroup.SFX)] = AudioSetting.m_AudioVolumeRange.DefaultSFX

---@class EDeviceLevel 裝置等級 為配合系統設置從0開始
EDeviceLevel =
{
    Low = 0,
    Medium = 1,
    High = 2,
}

NowDeviceLevel = EDeviceLevel.High