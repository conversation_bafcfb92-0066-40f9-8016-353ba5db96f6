---基本的Debug功能
---可以參考 RoleMgr 使用方法
---@class DebugMsgBase
DebugMsgBase = {}
DebugMsgBase.__index = DebugMsgBase

function DebugMsgBase:SetDebugMode(iIsOn)
    self.m_isDebugOn = iIsOn
end

function DebugMsgBase.New()
    local _table = {}
    _table.m_isDebugOn = false
    _table.m_PrefixMsg = ""
    _table.m_PrefixColor = "#FFFFFFFF"
    _table = setmetatable(_table, {
        __index = DebugMsgBase,
        __call = function (iSelf, iMsgType, iMsg)
            --檢查 Debug 有沒有開啟
            if not iSelf.m_isDebugOn then
                return
            end
            --加入前贅詞
            if not string.IsNullOrEmpty(iSelf.m_PrefixMsg) then
                iMsg = "<color="..iSelf.m_PrefixColor..">".. iSelf.m_PrefixMsg .."</color> ： "..iMsg
            end
            --加入訊息
            if iMsgType == EDebugMsgType.Log then
                D.Log(iMsg..debug.traceback())
            elseif iMsgType == EDebugMsgType.LogWarning then
                D.LogWarning(iMsg..debug.traceback())
            elseif iMsgType == EDebugMsgType.LogError then
                D.LogError(iMsg..debug.traceback())
            else
                --D.LogError("使用未知的 EDebugMsgType:" .. iMsgType)
                D.LogError("不合法的EDebugMsgType")
            end
        end
    })

    return _table
end