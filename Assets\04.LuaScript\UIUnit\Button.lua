---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2022 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------
---
---=====================================================================
---按鈕
---@class Button
---author 鼎翰
---version 1.0
---since [HEM 2.0]
---date 2025.04.27
Button = {}

---按鈕按下預設的音效 ID
local _OnCilckDefultAudioID = 31001

--- 檢查並初始化按鈕
---@param self table 按鈕實例
---@return table 初始化後的按鈕
local function EnsureButtonInitialized(self)
    if type(self) == "userdata" or self.m_ButtonEx == nil then
        return Button.New(self)
    end
    return self
end

--- 取得按鈕當前狀態
---@param iButton table 按鈕實例
---@return ESelectionState 按鈕的當前狀態
local function GetCurrentState(iButton)
    if iButton.m_ButtonEx.CurrentState == ESelectionState.Pressed then
        return iButton.m_ButtonEx.LastState
    else
        return iButton.m_ButtonEx.CurrentState
    end
end

---初始化 ButtonDelegateMgr
function Button.InitButtonDelegateMgr()
    -- 設定按鈕音效ID
    ButtonDelegateMgr.Inst.m_GetClickAudioIdx = function()
        return _OnCilckDefultAudioID
    end
    -- 設定播放按鈕音效
    ButtonDelegateMgr.Inst.m_ActionPlayAudio = function(iIndex)
        AudioMgr.PlayUIAudio(iIndex)
    end
    -- 設定 Scale Function
    ButtonDelegateMgr.Inst.m_StateTriggerDelegate_Scale = function(iGameObject, iVector3, iShowTime)
        LeanTween.scale(iGameObject, iVector3, iShowTime)
    end
    -- 設定 Move Function
    ButtonDelegateMgr.Inst.m_StateTriggerDelegate_Move = function(iRectTransform, iVector3, iMoveSecond)
        LeanTween.move(iRectTransform, iVector3, iMoveSecond)
    end
end

---新增按鈕
---@param iGObjButton GameObject 要加的按鈕 GameObject 
function Button.New(iGObjButton)
    local _Obj = type(iGObjButton) == "userdata" and iGObjButton or iGObjButton.gameObject
    ---取得按鈕的 Script
    if Extension.IsUnityObjectNull(_Obj) then
        return
    end
    ---做個功能 Button 的 table 給他
    local _Button = {}
    ---物件 本人
    _Button.gameObject = _Obj.gameObject
    _Button.transform = _Button.gameObject.transform
    ---取 _Script_ButtonEx
    local _IsButtonExists, _Script_ButtonEx = _Button.gameObject:TryGetComponent(typeof(ButtonEx), nil)
    -- 沒取到的話 加 Component
    if not _IsButtonExists then
        _Script_ButtonEx = _Button.gameObject:AddComponent(typeof(ButtonEx))
    end
    -- 設定 ButtonEx
    _Button.m_ButtonEx = _Script_ButtonEx
    -- 取得要繼承的 Table 或 userdata
    local _Parents = {}
    if type(iGObjButton) == "table" and iGObjButton.m_Script_EventTrigger ~= nil then
        _Button.m_Script_EventTrigger = iGObjButton.m_Script_EventTrigger
        _Parents = {Button, EventTriggerEx}
    else
        _Parents = {Button}
    end
    -- 設定元表
    setmetatable(_Button, {
        __index = function(t, ikey)
            if ikey == "m_Script_EventTrigger" and table.Count(_Parents) <= 1 then
                return nil
            end
            for key, value in pairs(_Parents) do
                if table.ContainsKey(value, ikey) then
                    return value[ikey]
                end
            end
            return nil
        end
    })

    return _Button
end

--- 加按鈕 Event
---@param iEventTriggerType EventTriggerType 要加的 TriggerType
---@param iEventDelegate LuaFunction 要加的 EventDelegate
function Button:AddListener(iEventTriggerType, iEventDelegate)
    self = EnsureButtonInitialized(self)
    if iEventTriggerType == EventTriggerType.PointerClick then
        self.m_ButtonEx.onClick:AddListener(iEventDelegate)
    else
        EventTriggerEx.AddListener(self, iEventTriggerType, iEventDelegate)
    end
end

---按鈕 觸發 OnPointerClick
function Button:OnPointerClick()
    if type(self) == "userdata" or self.m_ButtonEx == nil then
        self = Button.New(self)
    end
    self:OnEventTrigger(EventTriggerType.PointerClick)
end

--- 按鈕觸發事件
---@param iEventTriggerType EventTriggerType 要觸發的 TriggerType
function Button:OnEventTrigger(iEventTriggerType)
    self = EnsureButtonInitialized(self)
    if iEventTriggerType == EventTriggerType.PointerClick then
        self.m_ButtonEx:Invoke()
    else
        EventTriggerEx.OnEventTrigger(self, iEventTriggerType)
    end
end

---觸發按鈕動態
---@param iEventTriggerType EventTriggerType 要觸發的 TriggerType
function Button:DoButtonStateTransition(iEventTriggerType)
    self = EnsureButtonInitialized(self)
    if iEventTriggerType == EventTriggerType.PointerClick then
        self.m_ButtonEx.DoButtonStateTransition(self.m_ButtonEx, ESelectionState.Selected, true)
        self.m_ButtonEx:SwitchAndGroupDoOnPointerClick()
    elseif iEventTriggerType == EventTriggerType.PointerEnter then
        self.m_ButtonEx.DoButtonStateTransition(self.m_ButtonEx, ESelectionState.Highlighted, true)
    elseif iEventTriggerType == EventTriggerType.PointerExit then
        self.m_ButtonEx.DoButtonStateTransition(self.m_ButtonEx, ESelectionState.Normal, true)
    elseif iEventTriggerType == EventTriggerType.PointerDown then
        self.m_ButtonEx.DoButtonStateTransition(self.m_ButtonEx, ESelectionState.Press, true)
    elseif iEventTriggerType == EventTriggerType.PointerUp then
        self.m_ButtonEx.DoButtonStateTransition(self.m_ButtonEx, ESelectionState.Normal, true)
    end
end

--- 清空按鈕的 Event
---@param iEventTriggerType EventTriggerType 要觸發的 TriggerType [= nil 預設清空 EventTriggerType.PointerClick]
function Button:ClearListener(iEventTriggerType)
    self = EnsureButtonInitialized(self)
    iEventTriggerType = iEventTriggerType or EventTriggerType.PointerClick
    if iEventTriggerType == EventTriggerType.PointerClick then
        self.m_ButtonEx.onClick:RemoveAllListeners()
    else
        EventTriggerEx.RemoveAllListenersByType(self, iEventTriggerType)
    end
end

---取得按鈕按下的音效 ID
---@return number 按鈕的按下的音效 ID ( <0：無音效，=0：預設 ) 
function Button.GetAudioID()
    self = EnsureButtonInitialized(self)
    return self.m_ButtonEx.ClickAudioIndex
end

---設定按鈕按下的音效
---@param iAudioID number 按鈕的按下的音效 ID ( <0：無音效，=0：預設 ) 
function Button:SetAudioID(iAudioID)
    self = EnsureButtonInitialized(self)
    self.m_ButtonEx.ClickAudioIndex = iAudioID
end

---設定按鈕文字
---@param iString string 按鈕的文字
function Button:SetText(iString)
    self = EnsureButtonInitialized(self)
    self.m_ButtonEx.SetButtonText(self.m_ButtonEx, iString)
end

--- 設按鈕為 Select 或 Normal
---@param iIsSelect boolean 是否設為 Select
function Button:SetSelect(iIsSelect)
    self = EnsureButtonInitialized(self)
    local _ESelectionState = iIsSelect and ESelectionState.Selected or ESelectionState.Normal
    if self.m_ButtonEx.m_IsSwitchBtn and self.m_ButtonEx.CurrentState ~= _ESelectionState then
        self.m_ButtonEx:SwitchAndGroupDoOnPointerClick()
    else
        self.m_ButtonEx:DoButtonStateTransition(_ESelectionState, true)
    end
end

--- 設按鈕為 Enable
function Button:SetEnable()
    self = EnsureButtonInitialized(self)
    self.m_ButtonEx:DoButtonStateTransition(ESelectionState.Normal)
end

--- 設按鈕為 Disable
---@param iIsNeedCallback boolean 在 Disable 狀態下是否需要執行 Callback
function Button:SetDisable(iIsNeedCallback)
    self = EnsureButtonInitialized(self)
    iIsNeedCallback = iIsNeedCallback == nil and true or iIsNeedCallback
    self.m_ButtonEx:DoButtonStateTransition(ESelectionState.Disabled, iIsNeedCallback)
end

--- 是否為點擊狀態
---@return boolean 是否為點擊狀態
function Button:IsSelect()
    self = EnsureButtonInitialized(self)
    return GetCurrentState(self) == ESelectionState.Selected
end

--- 是否為正常狀態
---@return boolean 是否為正常狀態
function Button:IsNormal()
    self = EnsureButtonInitialized(self)
    return GetCurrentState(self) == ESelectionState.Normal
end

--- 是否為鼠標盤旋狀態
---@return boolean 是否為鼠標盤旋狀態
function Button:IsHover()
    self = EnsureButtonInitialized(self)
    return GetCurrentState(self) == ESelectionState.Highlighted
end

--- 是否為停用狀態
---@return boolean 是否為停用狀態
function Button:IsDisable()
    self = EnsureButtonInitialized(self)
    return GetCurrentState(self) == ESelectionState.Disabled
end
