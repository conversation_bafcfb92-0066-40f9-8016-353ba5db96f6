---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- private software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2021 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------
---
---=====================================================================

---音效管理
---@class AudioMgr
---author Hui
---version 1.0
---since [ProjectBase] 0.1
---date 2025.5.20
AudioMgr = {}
local private = AudioMgr

---是否為音效測試
private.m_IS_AUDIO_TEST = false
---是否顯示音效除錯訊息
private.m_IS_AUDIOMGR_LOG = false

--- 使用中的音效
local _Table_PlayingAudio = {}

---@type ChineseGamer.Utilities.ObjectPool<GameObject> 音效物件池
local _GObjPool_Audio = {}

local _MixerName = "AudioMixer"
--- 播放中背景音效 (Hash)
local _PlayingBGM = 0
--- 淡入中的背景音效 (Hash)
local _FadingBGM = 0

--- 是否 淡入 / 淡出 Master
local _IsFadeMaster = ""
--- 暫存音量用
local _MasterFadeVol = 0

--- 是否完成初始化
local _FinishInit = false

---@class EMixerGroup 混音器種類
AudioMgr.EMixerGroup = {
    Master = 0,
    --- 介面
    UI = 1,
    --- 背景音效
    BGM = 2,
    --- 語音
    SFX = 3,
    --- 王攻擊
    Boss = 4,
    --- 玩家攻擊
    Player = 5,
    ---NPC
    NPC = 6,
    --- 人聲
    Voice = 7,
}

---@class EAudioType 音效播放種類對應名稱
AudioMgr.EAudioType = {
    --- UI 音效
    UI = "UI_%s",
    --- 背景音效
    BGM = "BGM_%s",
    --- 聲音音效
    SFX = "SFX_%s",
    --- 人聲音效
    Voice = "Voice_%s",
}

require("Logic/Mgr/AudioMgr/AudioSetting")

--- 取得音效名稱
function AudioMgr.GetAudioName(iAudioType, iAudioId)
    D.Log("iAudioId: " .. iAudioId)
    return string.format(iAudioType, GFunction.Zero_stuffing(iAudioId, 5))
end

function AudioMgr.ShowLog(iStr, iColorStr, iIsError)
    if private.m_IS_AUDIOMGR_LOG and (iIsError ~= nil or not iIsError) then
        iColorStr = string.IsNullOrEmpty(iColorStr) and "yellow" or iColorStr
        D.Log(string.format("<color=%s>[AudioMgr]%s</color>", iColorStr, iStr))
    end

    if iIsError then
        D.LogError(string.format("<color=%s>[AudioMgr]%s</color>", iColorStr, iStr))
    end
end

local function ResetAudio(iGObj)
    private.ShowLog("ResetAudio")
    iGObj:SetActive(false)
end

local function OneTimeInitAudio(iGObj)
    private.ShowLog("OneTimeInitAudio")
    iGObj:AddComponent(typeof(AudioSource))
    iGObj:SetActive(false)
end

local function ResetPlayingAudio(iHashCode, iAudioType, iMixerGroup, iAudioId, iParent, iIsImportantAudio, iIsFollow, iLoop, iName, iIsActive, iSpeed)
    if ProjectMgr.IsEditor() then
        _Table_PlayingAudio[iHashCode].AudioObj.name = iName
    end

    _Table_PlayingAudio[iHashCode].AudioType = iAudioType
    _Table_PlayingAudio[iHashCode].iAudioId = iAudioId
    _Table_PlayingAudio[iHashCode].MixerGroup = iMixerGroup
    _Table_PlayingAudio[iHashCode].Time = Time.deltaTime
    _Table_PlayingAudio[iHashCode].IsImportantAudio = iIsImportantAudio
    _Table_PlayingAudio[iHashCode].AudioSource = _Table_PlayingAudio[iHashCode].AudioObj:GetComponent(typeof(AudioSource))
    _Table_PlayingAudio[iHashCode].AudioSource.outputAudioMixerGroup = private.m_MasterAudioMixerGroupAy[tostring(table.GetKey(AudioMgr.EMixerGroup, iMixerGroup))]
    _Table_PlayingAudio[iHashCode].AudioSource.volume = 1

    _Table_PlayingAudio[iHashCode].AudioSource.minDistance = AudioSetting.m_DefalutAudioSourceSetting.MinDistance
    _Table_PlayingAudio[iHashCode].AudioSource.maxDistance = AudioSetting.m_DefalutAudioSourceSetting.MaxDistance
    _Table_PlayingAudio[iHashCode].AudioSource.spread = AudioSetting.m_DefalutAudioSourceSetting.Spread
    _Table_PlayingAudio[iHashCode].AudioSource.spatialBlend = AudioSetting.m_DefalutAudioSourceSetting.SpatialBlend
    _Table_PlayingAudio[iHashCode].AudioSource.rolloffMode = AudioSetting.m_DefalutAudioSourceSetting.RolloffMode

    if not iIsFollow then
        _Table_PlayingAudio[iHashCode].AudioObj.transform:SetParent(private.m_Root[iAudioType])
        _Table_PlayingAudio[iHashCode].AudioObj.transform.localPosition = iParent.localPosition
    else
        _Table_PlayingAudio[iHashCode].AudioObj.transform:SetParent(iParent)
        _Table_PlayingAudio[iHashCode].AudioObj.transform.localPosition = Vector3.zero
    end

    if iAudioType == AudioMgr.EAudioType.BGM then
        _Table_PlayingAudio[iHashCode].AudioSource.spatialBlend = 0
    elseif iAudioType == AudioMgr.EAudioType.SFX then
        -- 3D 音效
        _Table_PlayingAudio[iHashCode].AudioSource.spatialBlend = 1
    elseif iAudioType == AudioMgr.EAudioType.UI and iAudioType == AudioMgr.EAudioType.Voice then
        _Table_PlayingAudio[iHashCode].AudioSource.spatialBlend = 0
    end

    _Table_PlayingAudio[iHashCode].AudioSource.loop = iLoop

    --- 聲音播放速度
    _Table_PlayingAudio[iHashCode].AudioSource.pitch = iSpeed and iSpeed or 1

    local _AudioName = private.GetAudioName(iAudioType, iAudioId)

    if iAudioId ~= 0 then
        local _DebugTraceback = debug.traceback()
        ResourceMgr.Load(_AudioName, function(iObj)
            if not Extension.IsUnityObjectNull(iObj) and _Table_PlayingAudio[iHashCode] then
                _Table_PlayingAudio[iHashCode].AudioSource.clip = iObj
                _Table_PlayingAudio[iHashCode].AudioObj:SetActive(iIsActive)
                -- Audio 被啟動後將可被回收的旗標開起來
                _Table_PlayingAudio[iHashCode].m_CanBeClear = true
                private.ShowLog("Audio Source is Active :" .. iHashCode)
            else
                D.LogError("找不到音效: " .. _AudioName .. "\n" .. _DebugTraceback)
            end
        end)
    end
end

---初始化
function AudioMgr.Init( iCallback )
    private.m_AudioMgr = GameObject("AudioMgr").transform
    -- private.m_AudioMgr:SetParent(GameObject.Find("MainCamera").transform)

    private.m_Root = {}
    for _Key, _Value in pairs(AudioMgr.EAudioType) do
        private.m_Root[_Value] = GameObject(_Key .. "_Root").transform
        private.m_Root[_Value]:SetParent(private.m_AudioMgr)
    end

    _GObjPool_Audio =
        Extension.GetGameObjPool(
        AudioSetting.m_MaxAudioPoolCount[table.GetKey(EDeviceLevel, NowDeviceLevel)],
        0,
        ResetAudio,
        OneTimeInitAudio
    )

    ResourceMgr.Load(
        _MixerName,
        function(iObj)
            if not Extension.IsUnityObjectNull(iObj) then
                private.m_AudioMixerObj = iObj
                local _MixerGroup = iObj:FindMatchingGroups("Master");
                private.m_MasterAudioMixerGroupAy = {}
                for i = 0, _MixerGroup.Length - 1 do
                    private.m_MasterAudioMixerGroupAy[_MixerGroup[i].name] = _MixerGroup[i]
                end

                _FinishInit = true

                -- private.PlaySpecialBGM(AudioMgr.EMixerGroup.BGM, AudioSetting.m_PlayingBGMKind.DownLoadBGM, true)

                for _Key, _Value in pairs(AudioSetting.m_AudioSwitch) do
                    private.SetMixerGrouVolume(AudioMgr.EMixerGroup[_Key], _Value == true and AudioSetting.m_AudioVolume[_Key] or 0)
                end
            end

            if iCallback then
                iCallback()
            end
        end,
        false
    )
end

--- 更新計時
local m_AudioUpdateTimeCount = 0
--- 特效刷新函式
function AudioMgr.Update()
    m_AudioUpdateTimeCount = m_AudioUpdateTimeCount + Time.deltaTime
    if m_AudioUpdateTimeCount >= AudioSetting.m_RefreshAudioPoolSecond then
        m_AudioUpdateTimeCount = 0
        for key, value in pairs(_Table_PlayingAudio) do
            -- 地塊特效不檢查回收
            if Extension.IsUnityObjectNull(value.AudioSource)
               or Extension.IsUnityObjectNull(value.AudioObj) then
                _Table_PlayingAudio[key] = nil
            elseif not value.AudioSource or (not value.AudioSource.loop and not value.AudioSource.isPlaying) then
                -- 避免出現音效還沒撥放就被回收的情況
                if value.m_CanBeClear ~= nil and value.m_CanBeClear == true then
                    private.ShowLog("回收: " .. value.AudioObj.name)
                    private.ReturnAudio(key)
                end
            end
        end
    end

    if not string.IsNullOrEmpty(_IsFadeMaster) then

        local _Vol = AudioSetting.m_AudioVolume.Master

        -- 設定_MasterFadeVol 始值
        if _MasterFadeVol == nil then
            _MasterFadeVol = _Vol
        end

        if _IsFadeMaster == "FadeOut" then
            _MasterFadeVol = Mathf.Lerp(_MasterFadeVol, 0, HEMTimeMgr.m_DeltaTime * AudioSetting.m_FadeOutSpeed)
            if _MasterFadeVol <= 0.01 then  -- 避免無限接近
                _MasterFadeVol = 0
                _IsFadeMaster = ""
            end
            AudioMgr.SetMixerGrouVolume(AudioMgr.EMixerGroup.Master, _MasterFadeVol)

        elseif _IsFadeMaster == "FadeIn" then
            _MasterFadeVol = Mathf.Lerp(_MasterFadeVol, _Vol, HEMTimeMgr.m_DeltaTime * AudioSetting.m_FadeInSpeed)
            if math.abs(_MasterFadeVol - _Vol) < 0.01 then  -- 避免無限接近
                _MasterFadeVol = _Vol
                _IsFadeMaster = ""
            end
            AudioMgr.SetMixerGrouVolume(AudioMgr.EMixerGroup.Master, _MasterFadeVol)
        end
    end

    -- 淡入中的音效不是 0 -> 正在切換音效中 (淡入音效中)
    if _FadingBGM ~= 0 then
        --- 有播放中的音效 -> 需要淡出
        if _PlayingBGM ~= 0 then
            local tempVolume = _Table_PlayingAudio[_PlayingBGM].AudioSource.volume - (AudioSetting.m_FadeOutSpeed * Time.deltaTime)
            if tempVolume > 0 then
                private.SetAudioVolume(_PlayingBGM, tempVolume)
            else
                private.SetAudioVolume(_PlayingBGM, 0)
                private.ReturnAudio(_PlayingBGM)
                _PlayingBGM = 0
            end
        end

        --- 淡入音效中
        if _PlayingBGM == 0 or AudioSetting.m_FadeInStartVolume > _Table_PlayingAudio[_PlayingBGM].AudioSource.volume then
            local tempBGMVolume = AudioSetting.m_AudioVolume.BGM
            local tempVolume
            if _Table_PlayingAudio[_FadingBGM] then
                tempVolume = _Table_PlayingAudio[_FadingBGM].AudioSource.volume + (AudioSetting.m_FadeInSpeed * Time.deltaTime)
            else
                tempVolume = 0
            end
            if tempVolume < tempBGMVolume then
                private.SetAudioVolume(_FadingBGM, tempVolume)
            else
                private.SetAudioVolume(_FadingBGM, tempBGMVolume)
                if _PlayingBGM == 0 then
                    _PlayingBGM = _FadingBGM
                    _FadingBGM = 0
                    _Table_PlayingAudio[_PlayingBGM].AudioObj.name = private.GetAudioName(_Table_PlayingAudio[_PlayingBGM].AudioType,
                        _Table_PlayingAudio[_PlayingBGM].iAudioId) .."_".._PlayingBGM.."_PlayingBGM"
                end
            end
        end
    end
end

---檢查可否播放音效
---只有 Sound 音效類型才會拿來判斷刪除
local function CheckCanPlayAudio(iAudioType, iMixerGroup, iAudioId, iParent, iIsImportantAudio)
    local _Result = true
    if not _FinishInit then
        private.ShowLog("音效系統尚未初始化完成: " .. iAudioId, "gray", true)
        _Result = false
    end

    if iAudioId == 0 then
        private.ShowLog("音效編號錯誤: " .. iAudioId, "gray", true)
        _Result = false
    end

    if iParent == nil then
        private.ShowLog("父物件 == nil: " .. iAudioId, "gray", true)
        _Result = false
    end

    -- 可播放音效是否滿了
    if _Result and table.Count(_Table_PlayingAudio) >= AudioSetting.m_MaxAudioPlayingCount[table.GetKey(EDeviceLevel, NowDeviceLevel)] then
        local _MinTime = Time.time;
        local _MinTimeKey = 0;
        local _ImprotantMinTime = Time.time;
        local _ImprotantMinTimeKey = 0;
        for key, value in pairs(_Table_PlayingAudio) do
            if value.AudioType == private.EAudioType.Sound and value.Time < _MinTime then
                if value.IsImportantAudio then
                    _ImprotantMinTime = value.Time
                    _ImprotantMinTimeKey = key
                else
                    _MinTime = value.Time
                    _MinTimeKey = key
                end
            end
        end

        -- 有沒有不重要效可以刪
        if _MinTimeKey == 0 then
            -- 要播的效是重要效
            if iIsImportantAudio then
                -- 找不到最舊的重要效 -> 不播了
                if _ImprotantMinTimeKey == 0 then
                    _Result = false
                    private.ShowLog("音效池滿了，且找不到效可以移除 (效系統有問題RRR，請洽相關人員)", "gray", true)
                -- 移除最舊的重要音效
                else
                    private.ReturnAudio(_ImprotantMinTimeKey)
                end
            -- 不是重要效 -> 不播了
            else
                _Result = false
            end
        else
            private.ReturnAudio(_MinTimeKey)
        end
    end

    return _Result
end

---播放任何音效都會進到這
---@param iAudioType EAudioType 音效種類
---@param iMixerGroup EMixerGroup MixerGroup
---@param iAudioId number uint 音效編號
---@param iParent Transform 座標
---@param iIsFollow boolean 要不要跟隨父物件
---@param IsLoop boolean 要不要循環播放
---@param iIsImportantAudio boolean 是不是重要的音效
---@param iLoadCompleteDelegate function 載入完成 CallBack
---@param iSpeed number 播放速度
local function PlayingAudio(iAudioType, iMixerGroup, iAudioId, iParent, iIsFollow, IsLoop, iIsImportantAudio, iLoadCompleteDelegate, iSpeed)
    if CheckCanPlayAudio(iAudioType, iMixerGroup, iAudioId, iParent, iIsImportantAudio) then
        local _TempObj = _GObjPool_Audio:Get()
        local _HashCode = _TempObj:GetHashCode()
        private.ShowLog("Playing: " .. _HashCode)
        _Table_PlayingAudio[_HashCode] = {}
        _Table_PlayingAudio[_HashCode].AudioObj = _TempObj
        ResetPlayingAudio(_HashCode, iAudioType, iMixerGroup, iAudioId, iParent, iIsImportantAudio,
            iIsFollow, IsLoop, private.GetAudioName(iAudioType, iAudioId) .."_".._HashCode, true, iSpeed)

        if iLoadCompleteDelegate then
            iLoadCompleteDelegate(_HashCode)

        end
    end
end

---還回音效
---@param iAudioType EAudioType 音效播放種類 iAudioType
---@param iHashCode number int 音效 HashCode
function AudioMgr.ReturnAudio(iHashCode)
    if not _Table_PlayingAudio[iHashCode] then
        do
            return
        end
    end

    private.ShowLog("ReturnAudio Hash: "..iHashCode, "orange")
    -- 歸還時 Id 給 0
    local _DefaultId = 0
    ResetPlayingAudio(iHashCode, _Table_PlayingAudio[iHashCode].AudioType, _Table_PlayingAudio[iHashCode].MixerGroup, _DefaultId,
    _Table_PlayingAudio[iHashCode].AudioObj.transform.parent, false, false, false,
            private.GetAudioName(_Table_PlayingAudio[iHashCode].AudioType, _DefaultId) .."_"..iHashCode, false)
    _GObjPool_Audio:Store(_Table_PlayingAudio[iHashCode].AudioObj)
    _Table_PlayingAudio[iHashCode] = nil
end

---還回場景環境音效
function AudioMgr.ReturnSceneEnvironmentAudio()
    if private.m_SceneEnvironmentAudio then
        local _tempCount = table.Count(private.m_SceneEnvironmentAudio)
        for i = 0, _tempCount do
            private.ReturnAudio(private.m_SceneEnvironmentAudio[i])
            private.m_SceneEnvironmentAudio[i] = nil
        end
    end
end

---調整音效聲音大小
---@param iHashCode int 音效 HashCode
---@param iVol float 聲音大小 ( 0~1 )
function AudioMgr.SetAudioVolume(iHashCode, iVol)
    if _Table_PlayingAudio[iHashCode] then
        _Table_PlayingAudio[iHashCode].AudioSource.volume =  iVol
    else
        private.ShowLog("SetAudioVolume 找不到音效: "..iHashCode)
    end
end

---調整混音器聲音大小
---可控制 AudioMgr.MixerGroup.Master / BGM / SFX / UI / Voice
---@param iMixerGroup AudioMgr.EMixerGroup 混音器種類
---@param iVol float 聲音大小 ( 0~1 )
function AudioMgr.SetMixerGrouVolume(iMixerGroup, iVol)
    local _MixerKey = tostring(table.GetKey(AudioMgr.EMixerGroup, iMixerGroup))
    -- 混音器是從 -80db ~ 20db
    local _Volume = iVol * (math.abs(AudioSetting.m_AudioMaxVolume[_MixerKey].Max - AudioSetting.m_AudioMaxVolume[_MixerKey].Min)) + AudioSetting.m_AudioMaxVolume[_MixerKey].Min
    --- 靜音按鈕控制
    if AudioSetting.m_AudioSwitch[_MixerKey] == true then
        private.m_MasterAudioMixerGroupAy[_MixerKey].audioMixer:SetFloat(_MixerKey, iVol > 0 and _Volume or AudioSetting.AudioMixerDB_Lowest)
    else
        private.m_MasterAudioMixerGroupAy[_MixerKey].audioMixer:SetFloat(_MixerKey, AudioSetting.AudioMixerDB_Lowest)
    end
end

--- 播放 CG 處理
---@param iPlayCG boolean 是否播放 CG
function AudioMgr.PlayCG(iPlayCG)
    if iPlayCG then
        -- 播放 GC master 淡出
        _MasterFadeVol = AudioSetting.m_AudioVolume.Master
        _IsFadeMaster = "FadeOut"
    else
        -- 結束 GC master 淡入
        _MasterFadeVol = 0
        _IsFadeMaster = "FadeIn"
    end
end

---播放特殊背景音效
---@param iPlayingBGMKind AudioSetting.PlayingBGMKind 播放的特殊背景音效種類
---@param iMixerGroup AudioMgr.MixerGroup MixerGroup
---@param iIsFade boolean 要不要淡入淡出
function AudioMgr.PlaySpecialBGM(iPlayingBGMKind, iMixerGroup, iIsFade)
    private.PlayBGM(iPlayingBGMKind, iMixerGroup, iIsFade, true)
end

---播放背景音效 (預設為重要音效)
---@param iAudioId number 播放的背景音效編號
---@param iIsFade boolean 要不要淡入淡出
---@param iLoadCompleteDelegate function 載入完成 CallBack
function AudioMgr.PlayBGM(iAudioId, iIsFade, iLoadCompleteDelegate)
    -- 重複播放中的背景音效 ID 且，沒有在但入淡出中的，不用淡入淡出
    -- TODO 還沒測過
    if _PlayingBGM and _Table_PlayingAudio[_PlayingBGM] and _Table_PlayingAudio[_PlayingBGM].iAudioId == iAudioId and _FadingBGM == 0 then
        if iLoadCompleteDelegate then
            iLoadCompleteDelegate()
        end
        do return end
    end

    PlayingAudio(AudioMgr.EAudioType.BGM, AudioMgr.EMixerGroup.BGM , iAudioId, private.m_Root[AudioMgr.EAudioType.BGM]
        , false, true, true, function(iHashCode)
            if iIsFade then
                if _FadingBGM ~= 0 then
                    local tempHash = _PlayingBGM
                    _PlayingBGM = _FadingBGM
                    if tempHash ~= 0 then
                        private.SetAudioVolume(tempHash, 0)
                        private.ReturnAudio(tempHash)
                    end
                end
                _FadingBGM = iHashCode
                private.SetAudioVolume(_FadingBGM, 0)
            else
                local tempHash = _FadingBGM
                _PlayingBGM = iHashCode
                _FadingBGM = 0
                if tempHash ~= 0 then
                    private.SetAudioVolume(tempHash, 0)
                    private.ReturnAudio(tempHash)
                end
                private.SetAudioVolume(_PlayingBGM, 1)
            end

            if iLoadCompleteDelegate then
                iLoadCompleteDelegate()
            end
    end)
end

---播放一般音效
---@param iAudioType AudioMgr.AudioType 音效種類
---@param iMixerGroup AudioMgr.MixerGroup MixerGroup
---@param iAudioId uint 音效編號
---@param iParent Transform 父物件
---@param iIsFollow boolean 要不要跟隨
---@param iIsImportantAudio boolean 是不是重要的音效
---@param iLoadCompleteDelegate function 載入完成 CallBack ( 會回傳 Hash，需要控制可取用 )
function AudioMgr.PlayAudio(iAudioType, iMixerGroup, iAudioId, iParent, iIsFollow, iIsImportantAudio, iLoadCompleteDelegate, iSpeed)
    PlayingAudio(iAudioType, iMixerGroup, iAudioId, iParent, iIsFollow, false, iIsImportantAudio, function(iHashCode)
            if iLoadCompleteDelegate then
                iLoadCompleteDelegate(iHashCode)
            end
    end, iSpeed)
end

---播放 UI 音效
---@param iAudioId uint 音效編號
---@param iIsImportantAudio boolean 是不是重要的音效
---@param iLoadCompleteDelegate function 載入完成 CallBack
function AudioMgr.PlayUIAudio(iAudioId, iIsImportantAudio, iLoadCompleteDelegate)
    PlayingAudio(AudioMgr.EAudioType.UI, AudioMgr.EMixerGroup.UI, iAudioId, private.m_Root[AudioMgr.EAudioType.UI], 
        false, false, iIsImportantAudio, function(iHashCode)
            if iLoadCompleteDelegate then
                iLoadCompleteDelegate(iHashCode)
            end
    end)
end

return AudioMgr
