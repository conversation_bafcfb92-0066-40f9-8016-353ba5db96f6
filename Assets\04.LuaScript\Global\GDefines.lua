---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not 
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2024 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------    
---
---=====================================================================
---專案共用 Define 請新增至此區
-- region require Lua
json = require("cjson")
-- endregion

-- region UnityEngine Object Reference
Application = CS.UnityEngine.Application
Resources = CS.UnityEngine.Resources
Screen = CS.UnityEngine.Screen
Input = CS.UnityEngine.Input
SystemInfo = CS.UnityEngine.SystemInfo
Camera = CS.UnityEngine.Camera
Random = CS.UnityEngine.Random
---@class Vector2
Vector2 = CS.UnityEngine.Vector2
---@class Vector3
Vector3 = CS.UnityEngine.Vector3
Vector4 = CS.UnityEngine.Vector4
Quaternion = CS.UnityEngine.Quaternion
Transform = CS.UnityEngine.Transform
GameObject = CS.UnityEngine.GameObject
Color = CS.UnityEngine.Color
Rect = CS.UnityEngine.Rect
RectTransformUtility = CS.UnityEngine.RectTransformUtility
RectTransform = CS.UnityEngine.RectTransform
Axis = CS.UnityEngine.RectTransform.Axis
CanvasGroup = CS.UnityEngine.CanvasGroup
Canvas = CS.UnityEngine.Canvas
TextAnchor = CS.UnityEngine.TextAnchor
Texture = CS.UnityEngine.Texture
Texture2D = CS.UnityEngine.Texture2D
RenderTexture = CS.UnityEngine.RenderTexture
Sprite = CS.UnityEngine.Sprite
---@class UnityEngine.SpriteRenderer
SpriteRenderer = CS.UnityEngine.SpriteRenderer
Material = CS.UnityEngine.Material
Shader = CS.UnityEngine.Shader
KeyCode = CS.UnityEngine.KeyCode
Image = CS.UnityEngine.UI.Image
RawImage = CS.UnityEngine.UI.RawImage
Toggle = CS.UnityEngine.UI.Toggle
Slider = CS.UnityEngine.UI.Slider
GridLayoutGroup = CS.UnityEngine.UI.GridLayoutGroup
InputField = CS.UnityEngine.UI.InputField
LayoutRebuilder = CS.UnityEngine.UI.LayoutRebuilder
AudioClip = CS.UnityEngine.AudioClip
AudioSource = CS.UnityEngine.AudioSource
Animator = CS.UnityEngine.Animator
RuntimeAnimatorController = CS.UnityEngine.RuntimeAnimatorController
BoxCollider = CS.UnityEngine.BoxCollider
---@class UnityEngine.BoxCollider2D
BoxCollider2D = CS.UnityEngine.BoxCollider2D
---@class UnityEngine.Rigidbody2D
Rigidbody2D = CS.UnityEngine.Rigidbody2D
ParticleSystem = CS.UnityEngine.ParticleSystem
PostProcessLayer = CS.UnityEngine.Rendering.PostProcessing.PostProcessLayer
PostProcessVolume = CS.UnityEngine.Rendering.PostProcessing.PostProcessVolume
PostProcessProfile = CS.UnityEngine.Rendering.PostProcessing.PostProcessProfile
DepthTextureMode = CS.UnityEngine.DepthTextureMode
MaterialPropertyBlock = CS.UnityEngine.MaterialPropertyBlock
Mathf = CS.UnityEngine.Mathf
WaitForSeconds = CS.UnityEngine.WaitForSeconds
WaitForEndOfFrame = CS.UnityEngine.WaitForEndOfFrame
WaitForFixedUpdate = CS.UnityEngine.WaitForFixedUpdate
Time = CS.UnityEngine.Time
EventSystem = CS.UnityEngine.EventSystems.EventSystem
EventTrigger = CS.UnityEngine.EventSystems.EventTrigger
Entry = CS.UnityEngine.EventSystems.EventTrigger.Entry
BaseEventData = CS.UnityEngine.EventSystems.BaseEventData
AxisEventData = CS.UnityEngine.EventSystems.AxisEventData
PointerEventData = CS.UnityEngine.EventSystems.PointerEventData
EventTriggerType = CS.UnityEngine.EventSystems.EventTriggerType
AudioRolloffMode = CS.UnityEngine.AudioRolloffMode
-- endregion

-- region System Object Reference
DateTime = CS.DateTime
String = CS.System.String
Stack = CS.System.Collections.Stack
Queue = CS.System.Collections.Queue
-- endregion

TextMeshProUGUI = CS.TMPro.TextMeshProUGUI
LeanTween = CS.LeanTween
LTDescr = CS.LTDescr

-- region Custom Object Reference
D = CS.GameTools.Log.D
AES = CS.AES
Extension = CS.Extension
TextureMgr = CS.TextureMgr
ResourceMgr = CS.ResourceMgr
ClientSocket = CS.ClientSocket
NoiseSettings = CS.NoiseSettings
CullingGroupMgr = CS.CullingGroupMgr
UIBoundaryCtrl = CS.UIBoundaryCtrl
UIBoundaryTarget = CS.UIBoundaryTarget
ViewRef = CS.ViewRef
ButtonDelegateMgr = CS.GameTools.UIExtension.ButtonDelegateMgr
ButtonEx = CS.GameTools.UIExtension.ButtonEx
UIGroupButtonCtrl = CS.GameTools.UIExtension.UIGroupButtonCtrl
UIRenderCtrl = CS.GameTools.UIExtension.UIRenderCtrl
UIImageChange = CS.GameTools.UIExtension.UIImageChange
UIRenderActive = CS.GameTools.UIExtension.UIRenderActive
UIRenderChangeColor = CS.GameTools.UIExtension.UIRenderChangeColor
UIRenderMove = CS.GameTools.UIExtension.UIRenderMove
UIRenderRotate = CS.GameTools.UIExtension.UIRenderRotate
UIRenderScale = CS.GameTools.UIExtension.UIRenderScale
UIRenderTMPTextChangeStyle = CS.GameTools.UIExtension.UIRenderTMPTextChangeStyle

SpriteMgr = CS.GameTools.Sprite.SpriteMgr
ProjectMgr = CS.GameTools.Project.ProjectMgr
FTPFileDownloadMgr = CS.GameTools.FTP.FTPFileDownloadMgr
JsonTools = CS.GameTools.Json.JsonTools

-- endregion

